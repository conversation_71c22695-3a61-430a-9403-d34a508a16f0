# To Do

  * Core API
      * Load imbalanced task output
      * Other dependence types
          * W/V cycles
          * 2D, 3D versions of stencil, FFT
      * Other kernel types
          * I/O bound
      * Recursive task graphs?
      * GPUs?
      * Measure memory usage of runtimes
  * Potential Implementations
      * DARMA
      * GASNet
      * Habanero
      * Hadoop
      * HPX
      * Julia
      * Nimbus
      * OCR
      * OpenSHMEM
      * Ray
      * UPC
