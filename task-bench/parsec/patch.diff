diff --git a/parsec/include/parsec/parsec_config_bottom.h b/parsec/include/parsec/parsec_config_bottom.h
index 748c310..8dba995 100644
--- a/parsec/include/parsec/parsec_config_bottom.h
+++ b/parsec/include/parsec/parsec_config_bottom.h
@@ -102,11 +102,11 @@ typedef int32_t parsec_dependency_t;
  * A set of constants defining the capabilities of the underlying
  * runtime.
  */
-#define MAX_LOCAL_COUNT  20
+#define MAX_LOCAL_COUNT  30
 #define MAX_PARAM_COUNT  20
 
 #define MAX_DEP_IN_COUNT  10
-#define MAX_DEP_OUT_COUNT 10
+#define MAX_DEP_OUT_COUNT 15
 
 #define MAX_TASK_STRLEN 128
 
