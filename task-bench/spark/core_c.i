/* Copyright 2020 Stanford University
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

%module core_c
%{
#include "core_c.h"
%}

%include "myTypemaps.i" /* argc, argv, and input_ptr are specifically mapped in myTypemaps */
%apply char* NOCOPYBYTE { char *output_ptr, char *scratch_ptr};
%apply size_t* LONG { size_t* input_bytes}; 

%include "core_c.h"

