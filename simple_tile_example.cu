#include <cuda/experimental/stf.cuh>
#include <iostream>
#include <vector>

using namespace cuda::experimental::stf;

/**
 * @brief Simplified tile data structure for demonstration
 */
template <typename T>
struct simple_tile_t
{
  size_t rows, cols;
  T* data;
  
  simple_tile_t(size_t rows, size_t cols, T* data = nullptr)
    : rows(rows), cols(cols), data(data) {}
  
  simple_tile_t() = default;
  
  __host__ __device__ T& operator()(size_t i, size_t j) {
    return data[i * cols + j];
  }
};

/**
 * @brief Shape specialization - defines metadata for memory management
 */
template <typename T>
class cuda::experimental::stf::shape_of<simple_tile_t<T>>
{
public:
  shape_of() = default;
  shape_of(const shape_of&) = default;
  shape_of(size_t rows, size_t cols) : rows(rows), cols(cols) {}
  shape_of(const simple_tile_t<T>& tile) : rows(tile.rows), cols(tile.cols) {}
  
  size_t size() const { return rows * cols; }
  
  size_t rows, cols;
};

/**
 * @brief Stream interface - implements memory operations
 */
template <typename T>
class simple_tile_stream_interface : public stream_data_interface_simple<simple_tile_t<T>>
{
public:
  using base = stream_data_interface_simple<simple_tile_t<T>>;
  
  simple_tile_stream_interface(simple_tile_t<T> tile) : base(std::move(tile)) {}
  simple_tile_stream_interface(typename base::shape_t shape) : base(shape) {}

  void stream_data_copy(const data_place& dst_memory_node, instance_id_t dst_instance_id,
                       const data_place& src_memory_node, instance_id_t src_instance_id,
                       cudaStream_t stream) override
  {
    const auto& src = this->instance(src_instance_id);
    const auto& dst = this->instance(dst_instance_id);
    size_t bytes = src.rows * src.cols * sizeof(T);
    
    cudaMemcpyKind kind = cudaMemcpyDeviceToDevice;
    if (src_memory_node.is_host()) kind = cudaMemcpyHostToDevice;
    if (dst_memory_node.is_host()) kind = cudaMemcpyDeviceToHost;
    
    cuda_safe_call(cudaMemcpyAsync(dst.data, src.data, bytes, kind, stream));
  }

  void stream_data_allocate(backend_ctx_untyped&, const data_place& memory_node,
                           instance_id_t instance_id, ::std::ptrdiff_t& s,
                           void**, cudaStream_t stream) override
  {
    auto& instance = this->instance(instance_id);
    size_t bytes = instance.rows * instance.cols * sizeof(T);
    
    T* ptr;
    if (memory_node.is_host()) {
      cuda_safe_call(cudaStreamSynchronize(stream));
      cuda_safe_call(cudaHostAlloc(&ptr, bytes, cudaHostAllocMapped));
    } else {
      cuda_safe_call(cudaMallocAsync(&ptr, bytes, stream));
    }
    
    instance.data = ptr;
    s = bytes;
  }

  void stream_data_deallocate(backend_ctx_untyped&, const data_place& memory_node,
                             instance_id_t instance_id, void*, cudaStream_t stream) override
  {
    auto& instance = this->instance(instance_id);
    
    if (memory_node.is_host()) {
      cuda_safe_call(cudaStreamSynchronize(stream));
      cuda_safe_call(cudaFreeHost(instance.data));
    } else {
      cuda_safe_call(cudaFreeAsync(instance.data, stream));
    }
  }
};

/**
 * @brief Interface association - connects data type to stream interface
 */
template <typename T>
struct cuda::experimental::stf::streamed_interface_of<simple_tile_t<T>>
{
  using type = simple_tile_stream_interface<T>;
};

// Simple kernel for demonstration
template <typename T>
__global__ void fill_tile(simple_tile_t<T> tile, T value)
{
  size_t i = blockIdx.x * blockDim.x + threadIdx.x;
  size_t j = blockIdx.y * blockDim.y + threadIdx.y;
  
  if (i < tile.rows && j < tile.cols) {
    tile(i, j) = value;
  }
}

int main()
{
  stream_ctx ctx;
  
  const size_t rows = 4, cols = 4;
  
  // Method 1: Create from existing data
  std::vector<float> host_data(rows * cols, 1.0f);
  simple_tile_t<float> tile_instance(rows, cols, host_data.data());
  auto ltile1 = ctx.logical_data(tile_instance);
  
  // Method 2: Create from shape (STF allocates memory)
  auto ltile2 = ctx.logical_data(shape_of<simple_tile_t<float>>(rows, cols));
  
  // Task 1: Fill first tile with value 5.0
  ctx.task(ltile1.write())->*[=](cudaStream_t stream, auto tile) {
    dim3 block(16, 16);
    dim3 grid((rows + 15) / 16, (cols + 15) / 16);
    fill_tile<<<grid, block, 0, stream>>>(tile, 5.0f);
  };
  
  // Task 2: Fill second tile with value 3.0
  ctx.task(ltile2.write())->*[=](cudaStream_t stream, auto tile) {
    dim3 block(16, 16);
    dim3 grid((rows + 15) / 16, (cols + 15) / 16);
    fill_tile<<<grid, block, 0, stream>>>(tile, 3.0f);
  };
  
  // Task 3: Copy from tile2 to tile1 (demonstrates data dependency)
  ctx.task(ltile1.write(), ltile2.read())->*[=](cudaStream_t stream, auto dst, auto src) {
    size_t bytes = rows * cols * sizeof(float);
    cuda_safe_call(cudaMemcpyAsync(dst.data, src.data, bytes, cudaMemcpyDeviceToDevice, stream));
  };
  
  ctx.finalize();
  
  // Verify result
  std::cout << "Result: " << host_data[0] << " (expected: 3.0)" << std::endl;
  std::cout << "Test " << (std::abs(host_data[0] - 3.0f) < 1e-6f ? "PASSED" : "FAILED") << std::endl;
  
  return 0;
}
