#include <cuda/experimental/stf.cuh>
#include <cuda_runtime.h>
#include <iostream>
#include <vector>
#include <cassert>

using namespace cuda::experimental::stf;

// Use the actual task-bench tile_t structure
typedef struct tile_s {
  float dep;
  char *output_buff;
} tile_t;

/**
 * @brief Shape specialization for task-bench tile_t
 * 
 * Since tile_t contains a pointer (output_buff), the shape needs to define
 * the size of the buffer that will be allocated for each instance.
 */
template <>
class cuda::experimental::stf::shape_of<tile_t>
{
public:
  /**
   * @brief Default constructor - required by CUDA STF
   */
  shape_of() : buffer_size(0) {}

  /**
   * @brief Constructor with buffer size specification
   */
  explicit shape_of(size_t buffer_size)
      : buffer_size(buffer_size)
  {}

  /**
   * @brief Copy constructor - required by CUDA STF
   */
  shape_of(const shape_of&) = default;

  /**
   * @brief Constructor from tile instance - required by CUDA STF
   * 
   * Note: We can't determine buffer size from the instance alone,
   * so this assumes a default size. In practice, you'd need to track
   * buffer sizes separately or modify tile_t to include size info.
   */
  shape_of(const tile_t& tile)
      : buffer_size(1024)  // Default size - should be configurable
  {
    (void)tile; // Suppress unused parameter warning
  }

  /**
   * @brief Returns total number of elements - required by CUDA STF
   * 
   * For tile_t, this includes space for the float dep and the buffer
   */
  size_t size() const
  {
    return sizeof(float) + buffer_size;
  }

  size_t buffer_size;  // Size of output_buff in bytes
};

/**
 * @brief Stream data interface for task-bench tile_t
 * 
 * Implements memory management for the tile_t structure, handling both
 * the float dep field and the char* output_buff allocation.
 */
class tile_stream_interface : public stream_data_interface_simple<tile_t>
{
public:
  using base = stream_data_interface_simple<tile_t>;
  using typename base::shape_t;

  /// Initialize from an existing tile
  tile_stream_interface(tile_t tile)
      : base(std::move(tile))
  {}

  /// Initialize from a tile shape
  tile_stream_interface(typename base::shape_t shape)
      : base(shape)
  {}

  /// Copy data between tile instances
  void stream_data_copy(
    const data_place& dst_memory_node,
    instance_id_t dst_instance_id,
    const data_place& src_memory_node,
    instance_id_t src_instance_id,
    cudaStream_t stream) override
  {
    assert(src_memory_node != dst_memory_node);

    const tile_t& src_instance = this->instance(src_instance_id);
    const tile_t& dst_instance = this->instance(dst_instance_id);

    cudaMemcpyKind kind = cudaMemcpyDeviceToDevice;
    if (src_memory_node.is_host())
    {
      kind = cudaMemcpyHostToDevice;
    }
    if (dst_memory_node.is_host())
    {
      kind = cudaMemcpyDeviceToHost;
    }

    // Copy the dep field
    cuda_safe_call(cudaMemcpyAsync(&dst_instance.dep, &src_instance.dep, 
                                  sizeof(float), kind, stream));

    // Copy the output buffer if both have valid pointers
    if (src_instance.output_buff && dst_instance.output_buff)
    {
      size_t buffer_size = this->shape.buffer_size;
      cuda_safe_call(cudaMemcpyAsync(dst_instance.output_buff, src_instance.output_buff,
                                    buffer_size, kind, stream));
    }
  }

  /// Allocate memory for a tile instance
  void stream_data_allocate(
    backend_ctx_untyped& /*unused*/,
    const data_place& memory_node,
    instance_id_t instance_id,
    ::std::ptrdiff_t& s,
    void** /*unused*/,
    cudaStream_t stream) override
  {
    tile_t& instance = this->instance(instance_id);
    size_t buffer_size = this->shape.buffer_size;

    char* buffer_ptr;

    if (memory_node.is_host())
    {
      // Synchronous host allocation
      cuda_safe_call(cudaStreamSynchronize(stream));
      cuda_safe_call(cudaHostAlloc(&buffer_ptr, buffer_size, cudaHostAllocMapped));
    }
    else
    {
      // Asynchronous device allocation
      cuda_safe_call(cudaMallocAsync(&buffer_ptr, buffer_size, stream));
    }

    // Initialize the tile structure
    instance.dep = 0.0f;
    instance.output_buff = buffer_ptr;

    // Positive value indicates successful allocation
    s = sizeof(float) + buffer_size;
  }

  /// Deallocate memory for a tile instance
  void stream_data_deallocate(
    backend_ctx_untyped& /*unused*/,
    const data_place& memory_node,
    instance_id_t instance_id,
    void* /*unused*/,
    cudaStream_t stream) override
  {
    tile_t& instance = this->instance(instance_id);
    
    if (instance.output_buff)
    {
      if (memory_node.is_host())
      {
        cuda_safe_call(cudaStreamSynchronize(stream));
        cuda_safe_call(cudaFreeHost(instance.output_buff));
      }
      else
      {
        cuda_safe_call(cudaFreeAsync(instance.output_buff, stream));
      }
      instance.output_buff = nullptr;
    }
  }

  /// Pin host memory for efficient transfers
  bool pin_host_memory(instance_id_t instance_id) override
  {
    tile_t& instance = this->instance(instance_id);
    if (!instance.output_buff)
    {
      return false;
    }

    size_t buffer_size = this->shape.buffer_size;
    cuda_safe_call(pin_memory(instance.output_buff, buffer_size));
    return true;
  }

  /// Unpin host memory
  void unpin_host_memory(instance_id_t instance_id) override
  {
    tile_t& instance = this->instance(instance_id);
    if (instance.output_buff)
    {
      unpin_memory(instance.output_buff);
    }
  }
};

/**
 * @brief Associate tile_t with its stream interface
 */
template <>
struct cuda::experimental::stf::streamed_interface_of<tile_t>
{
  using type = tile_stream_interface;
};

// Example CUDA kernel that works with task-bench tile_t
__global__ void process_tile_kernel(tile_t tile, float value, size_t buffer_size)
{
  int tid = blockIdx.x * blockDim.x + threadIdx.x;
  
  if (tid == 0)
  {
    // Update the dep field
    tile.dep = value;
    
    // Fill the output buffer with some pattern
    for (size_t i = 0; i < buffer_size && i < 1024; ++i)
    {
      tile.output_buff[i] = (char)(value + i);
    }
  }
}

/**
 * @brief Demonstration of task-bench tile_t usage with CUDA STF
 */
int main()
{
  stream_ctx ctx;

  const size_t buffer_size = 256;  // Size of output_buff

  // Method 1: Create from existing tile with host buffer
  std::vector<char> host_buffer(buffer_size, 0);
  tile_t host_tile = {1.5f, host_buffer.data()};
  auto ltile1 = ctx.logical_data(host_tile);
  ltile1.set_symbol("host_tile");

  // Method 2: Create from shape (STF will allocate memory)
  auto ltile2 = ctx.logical_data(shape_of<tile_t>(buffer_size));
  ltile2.set_symbol("allocated_tile");

  std::cout << "Created logical tiles with buffer size: " << buffer_size << std::endl;

  // Task 1: Process the host-initialized tile
  ctx.task(ltile1.write())->*[=](cudaStream_t stream, auto dtile) {
    dim3 block(256);
    dim3 grid(1);
    process_tile_kernel<<<grid, block, 0, stream>>>(dtile, 10.0f, buffer_size);
  };

  // Task 2: Process the STF-allocated tile
  ctx.task(ltile2.write())->*[=](cudaStream_t stream, auto dtile) {
    dim3 block(256);
    dim3 grid(1);
    process_tile_kernel<<<grid, block, 0, stream>>>(dtile, 20.0f, buffer_size);
  };

  // Task 3: Create a dependency between tiles (copy from tile2 to tile1)
  ctx.task(ltile1.write(), ltile2.read())->*[=](cudaStream_t stream, auto dst_tile, auto src_tile) {
    // Simple dependency: copy dep value and first few bytes of buffer
    cuda_safe_call(cudaMemcpyAsync(&dst_tile.dep, &src_tile.dep, sizeof(float), 
                                  cudaMemcpyDeviceToDevice, stream));
    cuda_safe_call(cudaMemcpyAsync(dst_tile.output_buff, src_tile.output_buff, 
                                  64, cudaMemcpyDeviceToDevice, stream));
  };

  // Finalize all tasks
  ctx.finalize();

  // Verify results on host
  std::cout << "Verification:" << std::endl;
  std::cout << "Final tile dep value: " << host_tile.dep << " (expected: 20.0)" << std::endl;
  std::cout << "First buffer byte: " << (int)host_buffer[0] << " (expected: " << (int)(20.0f + 0) << ")" << std::endl;

  // Check results
  bool success = (std::abs(host_tile.dep - 20.0f) < 1e-6f) && 
                 (host_buffer[0] == (char)(20.0f + 0));

  std::cout << "Test " << (success ? "PASSED" : "FAILED") << std::endl;

  return success ? 0 : 1;
}
