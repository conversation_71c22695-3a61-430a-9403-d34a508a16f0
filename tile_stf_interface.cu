#include <cuda/experimental/stf.cuh>
#include <cuda_runtime.h>
#include <iostream>
#include <vector>
#include <cassert>

using namespace cuda::experimental::stf;

/**
 * @brief A tile data structure representing a 2D block of data with tiling information
 * 
 * This represents a tile within a larger matrix, storing both the data and metadata
 * about its position and dimensions within the parent matrix.
 */
template <typename T>
class tile_t
{
public:
  tile_t(size_t tile_rows, size_t tile_cols, size_t parent_rows, size_t parent_cols, 
         size_t start_row, size_t start_col, T* data = nullptr)
      : tile_rows(tile_rows)
      , tile_cols(tile_cols)
      , parent_rows(parent_rows)
      , parent_cols(parent_cols)
      , start_row(start_row)
      , start_col(start_col)
      , data(data)
  {}

  // Default constructor for shape-only initialization
  tile_t() = default;

  __host__ __device__ T& operator()(size_t i, size_t j)
  {
    return data[i * tile_cols + j];
  }

  __host__ __device__ const T& operator()(size_t i, size_t j) const
  {
    return data[i * tile_cols + j];
  }

  // Get global coordinates within parent matrix
  __host__ __device__ size_t global_row(size_t local_row) const
  {
    return start_row + local_row;
  }

  __host__ __device__ size_t global_col(size_t local_col) const
  {
    return start_col + local_col;
  }

  size_t tile_rows, tile_cols;     // Dimensions of this tile
  size_t parent_rows, parent_cols; // Dimensions of parent matrix
  size_t start_row, start_col;     // Starting position in parent matrix
  T* data;                         // Pointer to tile data
};

/**
 * @brief Shape specialization for tile_t
 * 
 * Defines the shape information needed by CUDA STF for memory management
 * and task scheduling. The shape contains all metadata except the actual data pointer.
 */
template <typename T>
class cuda::experimental::stf::shape_of<tile_t<T>>
{
public:
  /**
   * @brief Default constructor - required by CUDA STF
   */
  shape_of() = default;

  /**
   * @brief Constructor from tile dimensions and position
   */
  explicit shape_of(size_t tile_rows, size_t tile_cols, size_t parent_rows, size_t parent_cols,
                   size_t start_row, size_t start_col)
      : tile_rows(tile_rows)
      , tile_cols(tile_cols)
      , parent_rows(parent_rows)
      , parent_cols(parent_cols)
      , start_row(start_row)
      , start_col(start_col)
  {}

  /**
   * @brief Copy constructor - required by CUDA STF
   */
  shape_of(const shape_of&) = default;

  /**
   * @brief Constructor from tile instance - required by CUDA STF
   * 
   * Extracts shape information from an existing tile instance
   */
  shape_of(const tile_t<T>& tile)
      : shape_of<tile_t<T>>(tile.tile_rows, tile.tile_cols, tile.parent_rows, tile.parent_cols,
                           tile.start_row, tile.start_col)
  {}

  /**
   * @brief Returns total number of elements - required by CUDA STF
   */
  size_t size() const
  {
    return tile_rows * tile_cols;
  }

  // Tile metadata
  size_t tile_rows, tile_cols;
  size_t parent_rows, parent_cols;
  size_t start_row, start_col;
};

/**
 * @brief Stream data interface for tile_t
 * 
 * Implements the required memory management operations for CUDA STF stream backend
 */
template <typename T>
class tile_stream_interface : public stream_data_interface_simple<tile_t<T>>
{
public:
  using base = stream_data_interface_simple<tile_t<T>>;
  using typename base::shape_t;

  /// Initialize from an existing tile
  tile_stream_interface(tile_t<T> tile)
      : base(std::move(tile))
  {}

  /// Initialize from a tile shape
  tile_stream_interface(typename base::shape_t shape)
      : base(shape)
  {}

  /// Copy data between tile instances
  void stream_data_copy(
    const data_place& dst_memory_node,
    instance_id_t dst_instance_id,
    const data_place& src_memory_node,
    instance_id_t src_instance_id,
    cudaStream_t stream) override
  {
    assert(src_memory_node != dst_memory_node);

    cudaMemcpyKind kind = cudaMemcpyDeviceToDevice;
    if (src_memory_node.is_host())
    {
      kind = cudaMemcpyHostToDevice;
    }
    if (dst_memory_node.is_host())
    {
      kind = cudaMemcpyDeviceToHost;
    }

    const tile_t<T>& src_instance = this->instance(src_instance_id);
    const tile_t<T>& dst_instance = this->instance(dst_instance_id);

    size_t sz = src_instance.tile_rows * src_instance.tile_cols * sizeof(T);

    cuda_safe_call(cudaMemcpyAsync((void*) dst_instance.data, (void*) src_instance.data, sz, kind, stream));
  }

  /// Allocate memory for a tile instance
  void stream_data_allocate(
    backend_ctx_untyped& /*unused*/,
    const data_place& memory_node,
    instance_id_t instance_id,
    ::std::ptrdiff_t& s,
    void** /*unused*/,
    cudaStream_t stream) override
  {
    tile_t<T>& instance = this->instance(instance_id);
    size_t sz = instance.tile_rows * instance.tile_cols * sizeof(T);

    T* data_ptr;

    if (memory_node.is_host())
    {
      // Synchronous host allocation
      cuda_safe_call(cudaStreamSynchronize(stream));
      cuda_safe_call(cudaHostAlloc(&data_ptr, sz, cudaHostAllocMapped));
    }
    else
    {
      // Asynchronous device allocation
      cuda_safe_call(cudaMallocAsync(&data_ptr, sz, stream));
    }

    // Positive value indicates successful allocation
    s = sz;
    instance.data = data_ptr;
  }

  /// Deallocate memory for a tile instance
  void stream_data_deallocate(
    backend_ctx_untyped& /*unused*/,
    const data_place& memory_node,
    instance_id_t instance_id,
    void* /*unused*/,
    cudaStream_t stream) override
  {
    tile_t<T>& instance = this->instance(instance_id);
    
    if (memory_node.is_host())
    {
      cuda_safe_call(cudaStreamSynchronize(stream));
      cuda_safe_call(cudaFreeHost(instance.data));
    }
    else
    {
      cuda_safe_call(cudaFreeAsync(instance.data, stream));
    }
  }

  /// Pin host memory for efficient transfers
  bool pin_host_memory(instance_id_t instance_id) override
  {
    tile_t<T>& instance = this->instance(instance_id);
    if (!instance.data)
    {
      return false;
    }

    size_t sz = instance.tile_rows * instance.tile_cols * sizeof(T);
    cuda_safe_call(pin_memory(instance.data, sz));
    return true;
  }

  /// Unpin host memory
  void unpin_host_memory(instance_id_t instance_id) override
  {
    tile_t<T>& instance = this->instance(instance_id);
    unpin_memory(instance.data);
  }
};

/**
 * @brief Associate tile_t with its stream interface
 * 
 * This specialization tells CUDA STF which interface to use for tile_t in stream backend
 */
template <typename T>
struct cuda::experimental::stf::streamed_interface_of<tile_t<T>>
{
  using type = tile_stream_interface<T>;
};

// CUDA kernel for tile operations
template <typename T>
__global__ void tile_add_kernel(tile_t<T> a, tile_t<T> b, tile_t<T> result)
{
  size_t i = blockIdx.x * blockDim.x + threadIdx.x;
  size_t j = blockIdx.y * blockDim.y + threadIdx.y;
  
  if (i < a.tile_rows && j < a.tile_cols)
  {
    result(i, j) = a(i, j) + b(i, j);
  }
}

template <typename T>
__global__ void tile_init_kernel(tile_t<T> tile, T value)
{
  size_t i = blockIdx.x * blockDim.x + threadIdx.x;
  size_t j = blockIdx.y * blockDim.y + threadIdx.y;

  if (i < tile.tile_rows && j < tile.tile_cols)
  {
    tile(i, j) = value;
  }
}

/**
 * @brief Demonstration of tile_t usage with CUDA STF
 */
int main()
{
  stream_ctx ctx;

  // Define tile dimensions and parent matrix info
  const size_t tile_rows = 4;
  const size_t tile_cols = 4;
  const size_t parent_rows = 8;
  const size_t parent_cols = 8;

  // Create host data for initial tiles
  std::vector<float> host_data_a(tile_rows * tile_cols, 2.0f);
  std::vector<float> host_data_b(tile_rows * tile_cols, 3.0f);
  std::vector<float> host_data_result(tile_rows * tile_cols, 0.0f);

  // Create tile instances
  tile_t<float> tile_a(tile_rows, tile_cols, parent_rows, parent_cols, 0, 0, host_data_a.data());
  tile_t<float> tile_b(tile_rows, tile_cols, parent_rows, parent_cols, 0, 4, host_data_b.data());
  tile_t<float> tile_result(tile_rows, tile_cols, parent_rows, parent_cols, 4, 0, host_data_result.data());

  // Create logical data from tile instances
  auto ltile_a = ctx.logical_data(tile_a);
  auto ltile_b = ctx.logical_data(tile_b);
  auto ltile_result = ctx.logical_data(tile_result);

  // Set symbols for debugging
  ltile_a.set_symbol("tile_a");
  ltile_b.set_symbol("tile_b");
  ltile_result.set_symbol("tile_result");

  std::cout << "Created logical tiles with dimensions: " << tile_rows << "x" << tile_cols << std::endl;

  // Task 1: Initialize tile_a with value 5.0
  ctx.task(ltile_a.write())->*[=](cudaStream_t stream, auto dtile_a) {
    dim3 block(16, 16);
    dim3 grid((tile_rows + block.x - 1) / block.x, (tile_cols + block.y - 1) / block.y);
    tile_init_kernel<<<grid, block, 0, stream>>>(dtile_a, 5.0f);
  };

  // Task 2: Initialize tile_b with value 3.0
  ctx.task(ltile_b.write())->*[=](cudaStream_t stream, auto dtile_b) {
    dim3 block(16, 16);
    dim3 grid((tile_rows + block.x - 1) / block.x, (tile_cols + block.y - 1) / block.y);
    tile_init_kernel<<<grid, block, 0, stream>>>(dtile_b, 3.0f);
  };

  // Task 3: Add tiles A and B, store result in tile_result
  ctx.task(ltile_result.write(), ltile_a.read(), ltile_b.read())->*[=](cudaStream_t stream, auto dresult, auto da, auto db) {
    dim3 block(16, 16);
    dim3 grid((tile_rows + block.x - 1) / block.x, (tile_cols + block.y - 1) / block.y);
    tile_add_kernel<<<grid, block, 0, stream>>>(da, db, dresult);
  };

  // Task 4: Create a new tile from shape only (no initial data)
  auto ltile_temp = ctx.logical_data(shape_of<tile_t<float>>(tile_rows, tile_cols, parent_rows, parent_cols, 4, 4));
  ltile_temp.set_symbol("tile_temp");

  // Initialize the shape-created tile
  ctx.task(ltile_temp.write())->*[=](cudaStream_t stream, auto dtemp) {
    dim3 block(16, 16);
    dim3 grid((tile_rows + block.x - 1) / block.x, (tile_cols + block.y - 1) / block.y);
    tile_init_kernel<<<grid, block, 0, stream>>>(dtemp, 10.0f);
  };

  // Task 5: Add result and temp tiles
  ctx.task(ltile_result.rw(), ltile_temp.read())->*[=](cudaStream_t stream, auto dresult, auto dtemp) {
    dim3 block(16, 16);
    dim3 grid((tile_rows + block.x - 1) / block.x, (tile_cols + block.y - 1) / block.y);
    tile_add_kernel<<<grid, block, 0, stream>>>(dresult, dtemp, dresult);
  };

  // Finalize all tasks
  ctx.finalize();

  // Verify results on host
  std::cout << "Verification:" << std::endl;
  std::cout << "Expected result: 5.0 + 3.0 + 10.0 = 18.0" << std::endl;
  std::cout << "Actual result: " << host_data_result[0] << std::endl;

  // Check all elements
  bool success = true;
  for (size_t i = 0; i < tile_rows * tile_cols; ++i)
  {
    if (std::abs(host_data_result[i] - 18.0f) > 1e-6f)
    {
      success = false;
      break;
    }
  }

  std::cout << "Test " << (success ? "PASSED" : "FAILED") << std::endl;

  return success ? 0 : 1;
}
