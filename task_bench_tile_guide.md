# CUDA STF Interface for Task-Bench tile_t

This guide shows how to create CUDA STF logical data interfaces for the actual `tile_t` structure used in the task-bench framework.

## Task-Bench tile_t Structure

The task-bench framework defines `tile_t` as:

```c
typedef struct tile_s {
  float dep;
  char *output_buff;
} tile_t;
```

This structure represents:
- `dep`: A dependency value (float)
- `output_buff`: A pointer to output buffer data (allocated separately)

## Key Challenges

Unlike simple data structures, `tile_t` contains a pointer to dynamically allocated memory. This requires special handling in the CUDA STF interface:

1. **Shape Definition**: Must specify the size of the `output_buff`
2. **Memory Management**: Must allocate/deallocate both the structure and the buffer
3. **Copy Operations**: Must handle both the `dep` field and buffer contents

## Implementation Components

### 1. Shape Specialization

```cpp
template <>
class cuda::experimental::stf::shape_of<tile_t>
{
public:
  shape_of() : buffer_size(0) {}
  explicit shape_of(size_t buffer_size) : buffer_size(buffer_size) {}
  shape_of(const shape_of&) = default;
  shape_of(const tile_t& tile) : buffer_size(1024) {} // Default size
  
  size_t size() const { return sizeof(float) + buffer_size; }
  
  size_t buffer_size;  // Size of output_buff in bytes
};
```

**Key Points:**
- Must track `buffer_size` since it's not stored in `tile_t`
- `size()` returns total memory needed (struct + buffer)
- Constructor from instance uses default size (limitation of current design)

### 2. Stream Data Interface

```cpp
class tile_stream_interface : public stream_data_interface_simple<tile_t>
{
public:
  // Memory allocation: allocates buffer and sets pointers
  void stream_data_allocate(...) override {
    tile_t& instance = this->instance(instance_id);
    size_t buffer_size = this->shape.buffer_size;
    
    char* buffer_ptr;
    if (memory_node.is_host()) {
      cudaHostAlloc(&buffer_ptr, buffer_size, cudaHostAllocMapped);
    } else {
      cudaMallocAsync(&buffer_ptr, buffer_size, stream);
    }
    
    instance.dep = 0.0f;
    instance.output_buff = buffer_ptr;
  }
  
  // Copy operation: copies both dep and buffer contents
  void stream_data_copy(...) override {
    // Copy dep field
    cudaMemcpyAsync(&dst.dep, &src.dep, sizeof(float), kind, stream);
    
    // Copy buffer contents
    if (src.output_buff && dst.output_buff) {
      cudaMemcpyAsync(dst.output_buff, src.output_buff, buffer_size, kind, stream);
    }
  }
};
```

### 3. Interface Association

```cpp
template <>
struct cuda::experimental::stf::streamed_interface_of<tile_t>
{
  using type = tile_stream_interface;
};
```

## Usage Patterns

### Creating Logical Data

```cpp
// From existing tile with host buffer
std::vector<char> host_buffer(buffer_size, 0);
tile_t host_tile = {1.5f, host_buffer.data()};
auto ltile = ctx.logical_data(host_tile);

// From shape (STF allocates memory)
auto ltile2 = ctx.logical_data(shape_of<tile_t>(buffer_size));
```

### Using in Tasks

```cpp
// Process tile in CUDA kernel
ctx.task(ltile.write())->*[=](cudaStream_t stream, auto dtile) {
  process_tile_kernel<<<grid, block, 0, stream>>>(dtile, value, buffer_size);
};

// Task with dependencies
ctx.task(output_tile.write(), input_tile.read())->*[=](cudaStream_t stream, auto out, auto in) {
  // Copy dep value
  cudaMemcpyAsync(&out.dep, &in.dep, sizeof(float), cudaMemcpyDeviceToDevice, stream);
  // Process buffer
  process_dependency_kernel<<<grid, block, 0, stream>>>(out, in);
};
```

## Integration with Task-Bench

For task-bench integration, you would typically:

1. **Create tiles for each task output**:
```cpp
auto output_tile = ctx.logical_data(shape_of<tile_t>(graph.output_bytes_per_task));
```

2. **Handle task dependencies**:
```cpp
// Task with multiple input dependencies
std::vector<logical_data<tile_t>> input_tiles = get_dependency_tiles(deps);
ctx.task(output_tile.write(), input_tiles[0].read(), input_tiles[1].read())
   ->*[=](cudaStream_t stream, auto out, auto in1, auto in2) {
     // Execute task-bench kernel with dependencies
     task_bench_kernel<<<grid, block, 0, stream>>>(out, in1, in2);
   };
```

3. **Execute task-bench core logic**:
```cpp
// In kernel or host code
graph.execute_point(timestep, point, 
                   tile.output_buff, graph.output_bytes_per_task,
                   input_ptrs.data(), input_bytes.data(), input_ptrs.size(),
                   scratch_ptr, graph.scratch_bytes_per_task);
```

## Limitations and Considerations

1. **Buffer Size Tracking**: The current `tile_t` doesn't store buffer size, so it must be tracked separately in the shape.

2. **Memory Layout**: The interface allocates `output_buff` separately from the `tile_t` structure itself.

3. **Host/Device Compatibility**: The `char*` pointer in `tile_t` needs different handling for host vs device memory.

4. **Task-Bench Integration**: May need modifications to work seamlessly with existing task-bench patterns that assume direct buffer management.

## Potential Improvements

1. **Extended tile_t**: Add buffer size to the structure:
```c
typedef struct tile_s {
  float dep;
  char *output_buff;
  size_t buffer_size;  // Add this field
} tile_t;
```

2. **Buffer Pool**: Use STF's memory management for buffer allocation rather than separate malloc/free.

3. **Unified Memory**: Consider using CUDA unified memory for simpler host/device handling.

This interface provides the foundation for integrating task-bench's `tile_t` with CUDA STF's logical data system while maintaining compatibility with existing task-bench patterns.
