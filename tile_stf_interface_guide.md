# CUDA STF Custom Data Interface Guide: tile_t Implementation

This guide demonstrates how to create custom CUDA STF logical data interfaces using a `tile_t` data type as an example. The implementation shows all essential components needed for custom data types to work seamlessly with CUDA STF's logical data system.

## Overview

To create a custom data type that works with CUDA STF, you need to implement three main components:

1. **Data Type Definition** - The actual data structure
2. **Shape Specialization** - Template specialization of `shape_of<T>`
3. **Interface Specialization** - Template specialization of `streamed_interface_of<T>`
4. **Stream Data Interface** - Implementation of memory management operations

## 1. Data Type Definition

```cpp
template <typename T>
class tile_t
{
public:
  // Constructor with tile metadata
  tile_t(size_t tile_rows, size_t tile_cols, size_t parent_rows, size_t parent_cols, 
         size_t start_row, size_t start_col, T* data = nullptr);
  
  // Default constructor for shape-only initialization
  tile_t() = default;

  // Data access operators
  __host__ __device__ T& operator()(size_t i, size_t j);
  __host__ __device__ const T& operator()(size_t i, size_t j) const;

  // Metadata
  size_t tile_rows, tile_cols;     // Tile dimensions
  size_t parent_rows, parent_cols; // Parent matrix dimensions
  size_t start_row, start_col;     // Position in parent matrix
  T* data;                         // Data pointer
};
```

**Key Requirements:**
- Must be default constructible
- Should provide data access methods
- Can contain metadata beyond just data pointer
- Data pointer can be null for shape-only instances

## 2. Shape Specialization

```cpp
template <typename T>
class cuda::experimental::stf::shape_of<tile_t<T>>
{
public:
  // Required constructors
  shape_of() = default;                           // Default constructor
  shape_of(const shape_of&) = default;           // Copy constructor
  shape_of(const tile_t<T>& tile);               // From instance constructor
  
  // Custom constructor for shape creation
  explicit shape_of(size_t tile_rows, size_t tile_cols, ...);
  
  // Required method: total number of elements
  size_t size() const { return tile_rows * tile_cols; }

  // Shape metadata (everything except data pointer)
  size_t tile_rows, tile_cols;
  size_t parent_rows, parent_cols;
  size_t start_row, start_col;
};
```

**Key Requirements:**
- Must specialize `cuda::experimental::stf::shape_of<YourType>`
- Must provide default constructor, copy constructor, and constructor from instance
- Must implement `size()` method returning total number of elements
- Should contain all metadata needed for memory allocation (but not data pointers)

## 3. Stream Data Interface Implementation

```cpp
template <typename T>
class tile_stream_interface : public stream_data_interface_simple<tile_t<T>>
{
public:
  using base = stream_data_interface_simple<tile_t<T>>;
  using typename base::shape_t;

  // Constructors
  tile_stream_interface(tile_t<T> tile);           // From instance
  tile_stream_interface(typename base::shape_t shape); // From shape

  // Required virtual methods
  void stream_data_copy(...) override;      // Copy between instances
  void stream_data_allocate(...) override; // Allocate memory
  void stream_data_deallocate(...) override; // Deallocate memory
  
  // Optional methods for host memory optimization
  bool pin_host_memory(instance_id_t instance_id) override;
  void unpin_host_memory(instance_id_t instance_id) override;
};
```

**Key Methods:**

### `stream_data_copy`
- Copies data between different memory locations (host/device)
- Must handle all combinations: host→device, device→host, device→device
- Uses `cudaMemcpyAsync` with appropriate `cudaMemcpyKind`

### `stream_data_allocate`
- Allocates memory for a data instance
- Must set `s` parameter to positive value on success, negative on failure
- Host allocation: use `cudaHostAlloc` with `cudaHostAllocMapped`
- Device allocation: use `cudaMallocAsync`

### `stream_data_deallocate`
- Deallocates memory for a data instance
- Host deallocation: use `cudaFreeHost`
- Device deallocation: use `cudaFreeAsync`

## 4. Interface Association

```cpp
template <typename T>
struct cuda::experimental::stf::streamed_interface_of<tile_t<T>>
{
  using type = tile_stream_interface<T>;
};
```

This specialization tells CUDA STF which interface class to use for your data type in the stream backend.

## Usage Patterns

### Creating Logical Data from Instance
```cpp
// Create tile with host data
tile_t<float> my_tile(4, 4, 8, 8, 0, 0, host_data.data());
auto logical_tile = ctx.logical_data(my_tile);
```

### Creating Logical Data from Shape
```cpp
// Create tile from shape only (STF will allocate memory)
auto logical_tile = ctx.logical_data(shape_of<tile_t<float>>(4, 4, 8, 8, 0, 0));
```

### Using in Tasks
```cpp
// Task with multiple tile dependencies
ctx.task(result_tile.write(), tile_a.read(), tile_b.read())
   ->*[=](cudaStream_t stream, auto dresult, auto da, auto db) {
     // Launch CUDA kernel using tile data
     my_kernel<<<grid, block, 0, stream>>>(da, db, dresult);
   };
```

## Memory Management

CUDA STF handles the complete memory lifecycle:

1. **Lazy Allocation**: Memory is allocated on first access per memory location
2. **Automatic Transfers**: Data is copied between host/device as needed
3. **Reference Counting**: Memory is automatically freed when no longer needed
4. **MSI Protocol**: Ensures data coherency across different memory locations

## Best Practices

1. **Separate Data from Metadata**: Keep data pointers separate from shape information
2. **Handle All Memory Types**: Implement both host and device allocation paths
3. **Use Async Operations**: Prefer async CUDA operations when available
4. **Pin Host Memory**: Implement pinning for efficient host↔device transfers
5. **Error Handling**: Use `cuda_safe_call` for proper error checking
6. **Symbols for Debugging**: Set symbols on logical data for easier debugging

## Testing Your Implementation

The example includes verification that:
- Logical data creation works from both instances and shapes
- Tasks can read/write tile data correctly
- Memory management handles allocation/deallocation properly
- Data transfers work between host and device
- Multiple tasks can depend on the same logical data

This pattern can be adapted for any custom data structure you need to use with CUDA STF.
